<script lang="ts" setup>
import { isEqual } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import AcCpu from '@/components/system/statusMonitor/acCpu.vue'
import AcInfoBox from '@/components/system/statusMonitor/acInfo.vue'
import AcMemory from '@/components/system/statusMonitor/acMemory.vue'
import AcNetwork from '@/components/system/statusMonitor/acNetwork.vue'

const { t } = useI18n()

const mainList = ref([])
const cpuList = ref([])
const memoryList = ref([])
const mainTimer: any = ref(null)

onMounted(() => {
  // 清除定时器
  clearInterval(mainTimer.value)
  mainTimer.value = null

  // 加载请求
  getDataList()
  setInterval(() => {
    getDataList()
  }, 15000)
})
onUnmounted(() => {
  // 清除定时器
  clearInterval(mainTimer.value)
  mainTimer.value = null
})

const num = ref(0)

// 存储上一次数据的引用
const prevData = ref({
  cpu: [],
  memory: [],
  main: [],
})

async function getDataList() {
  try {
    const data = await $api('', { requestType: 221 })

    if (data?.err_code !== 0 || !data?.info) {
      console.error('API Error:', data?.err_msg || 'Unknown error')

      return
    }

    const { system, network } = data.info
    const newCpu = system?.cpu_history || []
    const newMemory = system?.memory_history || []
    const newMain = network?.br_wan?.rate_history || []

    // 深度对比三个核心数据字段
    const isDataChanged
      = !isEqual(newCpu, prevData.value.cpu)
      || !isEqual(newMemory, prevData.value.memory)
      || !isEqual(newMain, prevData.value.main)

    if (isDataChanged) {
      // 更新视图数据
      cpuList.value = newCpu
      memoryList.value = newMemory
      mainList.value = newMain

      // 存储当前数据用于下次对比
      prevData.value = {
        cpu: JSON.parse(JSON.stringify(newCpu)),
        memory: JSON.parse(JSON.stringify(newMemory)),
        main: JSON.parse(JSON.stringify(newMain)),
      }

      // 触发计数变更
      num.value++
    }
  }
  catch (error) {
    console.error('请求异常:', error)
  }
}
</script>

<template>
  <VRow class="match-height">
    <!-- 👉 AC信息 -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('SystemStatus.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('SystemStatus.CurrentSettings') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcInfoBox />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 AC 网络流速 -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('NetworkTraffic.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('NetworkTraffic.Subtitle') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcNetwork
            :key="num + 1"
            :main-list="mainList"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 AC CPU -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('CPUUsage.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('CPUUsage.Subtitle') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcCpu
            :key="num + 2"
            :cpu-list="cpuList"
          />
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 AC 内存 -->
    <VCol
      cols="12"
      md="6"
    >
      <VCard>
        <VCardItem class="d-flex flex-wrap justify-space-between gap-4">
          <VCardTitle>{{ t('MemoryUsage.Title') }}</VCardTitle>
          <VCardSubtitle>{{ t('MemoryUsage.Subtitle') }}</VCardSubtitle>
        </VCardItem>
        <VCardText>
          <AcMemory
            :key="num + 3"
            :memory-list="memoryList"
          />
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/libs/apex-chart";
</style>
