import { ElMessage } from 'element-plus'
import { ofetch } from 'ofetch'
import { getI18n } from '@/plugins/i18n'

// 获取 i18n 实例的 t 函数
const getT = () => {
  const i18n = getI18n()

  return i18n.global.t
}

// 定义基础响应类型
interface ApiResponse<T = any> {
  err_code: number
  err_message: string
}

// 创建自定义 fetch 实例
const apiFetcher = ofetch.create({
  // 基础配置
  baseURL: sessionStorage.getItem('url'),
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
  },
  async onRequest({ options }) {
    const t = getT()
    const captcha = sessionStorage.getItem('captcha')
    if (!captcha && options.body?.requestType !== 305) {
      ElMessage.error(t('ApiErrors.PleaseLogin'))
      window.location.replace('/login')

      return
    }
    if (options.body) {
      options.body.captcha = captcha
      options.body = JSON.stringify(options.body)
    }
  },
  async onResponse({ response }) {
    const t = getT()

    // 处理响应数据
    if (response._data) {
      const data = response._data as ApiResponse

      console.log(data, 'data')

      // 根据业务代码处理不同情况
      if (data.err_code === 0) {
        // 成功，无需处理
      }
      else if (data.err_code === -1) {
        ElMessage.error(t('ApiErrors.SetGetFailed'))
      }
      else if (data.err_code === -2) {
        ElMessage.error(t('ApiErrors.JSONFormatError'))
      }
      else if (data.err_code === -4) {
        ElMessage.error(t('ApiErrors.UnknownRequestType'))
      }
      else if (data.err_code === -5) {
        ElMessage.error(t('ApiErrors.LoginFailed'))
      }
      else if (data.err_code === -6) {
        ElMessage.error(t('ApiErrors.InvalidValue'))
      }
      else if (data.err_code === -7) {
        ElMessage.error(t('ApiErrors.InsufficientSpace'))
      }
      else if (data.err_code === -8) {
        ElMessage.error(t('ApiErrors.GetInfoFailed'))
      }
      else if (data.err_code === -9) {
        ElMessage.error(t('ApiErrors.NotLoggedIn'))
        window.location.replace('/login')
      }
      else if (data.err_code === -10) {
        ElMessage.error(t('ApiErrors.ImportConfigError'))
      }
      else if (data.err_code === -11) {
        ElMessage.error(t('ApiErrors.ExportConfigError'))
      }
      else if (data.err_code === -12) {
        ElMessage.error(t('ApiErrors.SystemBusyOtherRequest'))
      }
      else if (data.err_code === -13) {
        ElMessage.error(t('ApiErrors.ProcessingOtherRequest'))
      }
      else if (data.err_code === -14) {
        ElMessage.error(t('ApiErrors.NetworkRestart'))
      }
      else if (data.err_code === -15) {
        ElMessage.error(t('ApiErrors.SystemRestart'))
      }
      else if (data.err_code === -18) {
        ElMessage.error(t('ApiErrors.UsernamePasswordError'))
      }
      else if (data.err_code === -19) {
        ElMessage.error(t('ApiErrors.FileError'))
      }
      else if (data.err_code === -20) {
        ElMessage.error(t('ApiErrors.RelaySettingError'))
      }
      else if (data.err_code === -21) {
        ElMessage.error(t('ApiErrors.SamePassword'))
      }
      else if (data.err_code === -22) {
        ElMessage.error(t('ApiErrors.UnknownRequestType'))
      }
      else {
        // 使用服务器返回的错误消息
        ElMessage.error(data.err_message)
      }

      response._data = data
    }
  },

  // 错误处理
  async onResponseError({ response }) {
    console.log(response, 'onResponseError')
    console.error('API Error:', response.status, response.statusText)
    if (response.status === 401)
      window.location.replace('/login')
    throw new Error('Network Error')
  },
})

// 封装 POST 请求
export function $api<T = any>(url: string, body?: Record<string, any>) {
  return apiFetcher<T>(url, {
    method: 'POST',
    body,
  })
}
